import React, {
  FunctionComponent,
  useContext,
  useEffect,
  useState,
} from "react";
import PageProps from "../../../models/PageProps.interface";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import Switch from "@mui/material/Switch";
import FormControlLabel from "@mui/material/FormControlLabel";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import TextField from "@mui/material/TextField";
import SearchOffIcon from "@mui/icons-material/SearchOff";
import TableRowsRoundedIcon from "@mui/icons-material/TableRowsRounded";

//Icons
import SearchRoundedIcon from "@mui/icons-material/SearchRounded";
import DriveFileRenameOutlineIcon from "@mui/icons-material/DriveFileRenameOutline";
import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
import LeftMenuComponent from "../../../components/leftMenu/leftMenu.component";
import AddEditUserComponent from "../../../components/addEditUser/addEditUser.component";
import UserService from "../../../services/user/user.service";
import { useDispatch, useSelector } from "react-redux";
import {
  IUser,
  IUsersListPaginatedResponse,
  IUsersListResponse,
} from "../../../interfaces/response/IUsersListResponse";
import ApplicationHelperService from "../../../services/ApplicationHelperService";
import {
  DEFAULT_PAGINATION,
  RoleType,
} from "../../../constants/dbConstant.constant";
import { IAlertDialogConfig } from "../../../interfaces/IAlertDialogConfig";
import AlertDialog from "../../../components/alertDialog/alertDialog.component";
import { ToastSeverity } from "../../../constants/toastSeverity.constant";
import ConfirmModel from "../../../components/confirmModel/confirmModel.component";
import { IUserResponseModel } from "../../../interfaces/response/IUserResponseModel";
import { ToastContext } from "../../../context/toast.context";
import { MessageConstants } from "../../../constants/message.constant";
import { LoadingContext } from "../../../context/loading.context";
import { Drawer, Grid, Grid2, Stack } from "@mui/material";
import { IUserCreationResponseModel } from "../../../interfaces/response/IUserCreationResponseModel";
import { IUserRequestModel } from "../../../interfaces/request/IUserRequestModel";
import Pagination from "@mui/material/Pagination";
import TablePagination from "@mui/material/TablePagination";
import { IPaginationModel } from "../../../interfaces/IPaginationModel";
import { IPaginationResponseModel } from "../../../interfaces/IPaginationResponseModel";
import GenericDrawer from "../../../components/genericDrawer/genericDrawer.component";
import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import { logOut } from "../../../actions/auth.actions";
import NoRowsFound from "../../../components/noRowsFound/noRowsFound.component";

type IDeleteRecord = {
  isShow: boolean;
  data: any;
  userId: number;
};

const Users: FunctionComponent<PageProps> = ({ title }) => {
  const dispatch = useDispatch();
  const [openAddEdit, setOpenAddEdit] = React.useState<IDeleteRecord>({
    isShow: false,
    data: null,
    userId: 0,
  });
  const { userInfo, rbAccess } = useSelector((state: any) => state.authReducer);
  const { setLoading } = useContext(LoadingContext);
  // const handleOpen = () => setOpenAddEdit(true);
  // const handleClose = () => setOpenAddEdit(false);
  const _userService = new UserService(dispatch);
  const _applicationHelperService = new ApplicationHelperService({});
  const [usersList, setUsersList] = useState<IUser[]>([]);
  const [allData, setAllData] = useState<IUser[]>([]);
  const [paginationModel, setPaginationModel] =
    useState<IPaginationModel>(DEFAULT_PAGINATION);
  const [paginationResponseModel, setPaginationResponseModel] =
    useState<IPaginationResponseModel>();
  const [showConfirmPopup, setShowConfirmPopup] = useState<IDeleteRecord>({
    isShow: false,
    data: null,
    userId: 0,
  });
  const { setToastConfig, setOpen } = useContext(ToastContext);
  const [alertConfig, setAlertConfig] = useState<IAlertDialogConfig>({
    isShow: false,
    callBack: () => undefined,
  });
  const [searchText, setSearchText] = useState("");
  const logoutUser = () => dispatch<any>(logOut());

  const fetchUsersPaginated = async () => {
    try {
      setLoading(true);
      const users: IUsersListPaginatedResponse =
        await _userService.getUsersPaginated(
          paginationModel,
          userInfo ? userInfo.id : 0
        );
      console.log("Users: ", users.results);
      setUsersList([...users.results]);
      setAllData([...users.results]);
      setPaginationResponseModel(users.pagination);
    } catch (error) {}

    setLoading(false);
  };

  useEffect(() => {
    if (paginationModel.pageNo > 0) {
      fetchUsersPaginated();
    }
  }, [paginationModel]);

  const deleteRecord = async () => {
    try {
      setShowConfirmPopup({ isShow: false, data: null, userId: 0 });
      setLoading(true);
      if (showConfirmPopup.data) {
        const response: IUserResponseModel = await _userService.deleteUser(
          (showConfirmPopup.data as IUser).id
        );

        if (response.list.affectedRows > 0) {
          setToastConfig(
            ToastSeverity.Success,
            MessageConstants.UserDeletedSuccessfully,
            true
          );
        } else {
          setToastConfig(
            ToastSeverity.Error,
            MessageConstants.UserDeletionFailed,
            true
          );
        }
        fetchUsersPaginated();
      }
    } catch (error) {}

    setLoading(false);
  };

  const assignUser = async (
    data: IUserRequestModel | undefined,
    userId: number,
    showToast: boolean
  ) => {
    try {
      setLoading(true);
      if (data) {
        const response: IUserResponseModel = await _userService.assignUser({
          ...data,
          userId: userId,
        });
        setOpenAddEdit({ isShow: false, data: null, userId: 0 });
        if (showToast) {
          if (response) {
            setToastConfig(
              ToastSeverity.Success,
              MessageConstants.LocationsAddedSuccessfully,
              true
            );
          } else {
            setToastConfig(
              ToastSeverity.Error,
              MessageConstants.UserAssignLocationsFailed,
              true
            );
          }
        }
      }
    } catch (error) {
      setToastConfig(
        ToastSeverity.Error,
        MessageConstants.ApiErrorStandardMessage,
        true
      );
    }

    setLoading(false);
  };

  const enableDisableUser = async (id: number, checked: boolean) => {
    try {
      setLoading(true);
      const response: IUserResponseModel = await _userService.enableDisableUser(
        id,
        {
          isActive: Number(checked),
        }
      );
      if (response.list.affectedRows > 0) {
        setToastConfig(
          ToastSeverity.Success,
          MessageConstants.UserUpdatedSuccessfully,
          true
        );
      } else {
        setToastConfig(
          ToastSeverity.Error,
          MessageConstants.UserUpdateFailed,
          true
        );
      }
      fetchUsersPaginated();
    } catch (error) {}
  };

  const [rowsPerPage, setRowsPerPage] = React.useState(10);

  const handleChangePage = (
    event: React.MouseEvent<HTMLButtonElement> | null,
    newPage: number
  ) => {};

  const handleChangeRowsPerPage = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setRowsPerPage(parseInt(event.target.value, 10));
  };

  const handleSearch = (e: any) => {
    const value = e.target.value;
    setSearchText(value);

    if (value.trim() === "") {
      // Show original data if search is cleared
      setUsersList(allData);
    } else {
      // Filter data
      const filtered = allData.filter((item) =>
        item.name.toLowerCase().includes(value.toLowerCase())
      );
      setUsersList(filtered);
    }
  };

  const showResetPasswordDialog = () => {
    setAlertConfig({
      isShow: true,
      title: "Reset Password",
      description: MessageConstants.PasswordResetSuccessfully,
      toastSeverity: ToastSeverity.Success,
      buttonTitle: "Okay!",
      callBack: () => logoutUser(),
    });
  };

  return (
    <div>
      <Box>
        <Box>
          <LeftMenuComponent>
            <Box>
              <h3 className="pageTitle">User Management</h3>
              <Box className="commonTableHeader">
                <Box>
                  <Box className="searchInput">
                    <TextField
                      hiddenLabel
                      id="filled-hidden-label-small"
                      defaultValue=""
                      placeholder="Search User"
                      variant="filled"
                      size="small"
                      onChange={handleSearch}
                    />
                    <span className="placeHolderIcon">
                      <SearchRoundedIcon />
                    </span>
                  </Box>
                </Box>
                {Boolean(rbAccess && rbAccess.UserCreate) && (
                  <Button
                    onClick={() =>
                      setOpenAddEdit({ isShow: true, data: null, userId: 0 })
                    }
                    className="tableActionBtn"
                    sx={{
                      minHeight: "50px", // Set the desired height
                    }}
                    startIcon={<AddOutlinedIcon />}
                  >
                    <span className="responsiveHide">Add User</span>
                  </Button>
                )}
              </Box>
              <Box>
                <TableContainer className="commonTable">
                  <Table aria-label="simple table">
                    <TableHead>
                      <TableRow>
                        {/* <TableCell>S.No</TableCell> */}
                        <TableCell>Name</TableCell>
                        <TableCell>Email</TableCell>
                        <TableCell>Mobile</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Created On</TableCell>
                        <TableCell>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {usersList.length > 0 ? (
                        usersList.map((user: IUser, index: number) => (
                          <TableRow key={index}>
                            {/* <TableCell scope="row" data-label="S.No">
                              {index + 1}
                            </TableCell> */}
                            <TableCell className="gridResponsiveTextLeft">
                              <Box className="tableUserInfo">
                                <Box>
                                  <img
                                    alt="MyLocoBiz - User Profile"
                                    className="width100"
                                    src={require("../../../assets/common/dummyProfile.png")}
                                  />
                                </Box>
                                <Box>
                                  <Typography>{user.name}</Typography>
                                  <Typography
                                    className={
                                      userInfo &&
                                      userInfo.roleId &&
                                      user.roleId === RoleType.Admin
                                        ? "badgeTextAdmin"
                                        : user.roleId === RoleType.Manager
                                        ? "badgeTextManager"
                                        : "badgeTextUser"
                                    }
                                  >
                                    {user.role}
                                  </Typography>
                                </Box>
                              </Box>
                            </TableCell>
                            <TableCell data-label="Email">
                              {user.email}
                            </TableCell>
                            <TableCell data-label="Mobile">
                              {user.mobile}
                            </TableCell>
                            <TableCell data-label="Status">
                              <FormControlLabel
                                control={
                                  <Switch
                                    disabled={
                                      !Boolean(rbAccess && rbAccess.UserEdit)
                                    }
                                    checked={Boolean(user.isActive)}
                                    onChange={(
                                      event: React.ChangeEvent<HTMLInputElement>,
                                      checked: boolean
                                    ) => {
                                      enableDisableUser(user.id, checked);
                                    }}
                                    className={
                                      !Boolean(rbAccess && rbAccess.UserEdit)
                                        ? " not-allowed"
                                        : ""
                                    }
                                  />
                                }
                                label=""
                              />
                            </TableCell>
                            <TableCell data-label="Location">
                              {_applicationHelperService.getExpandedDateTimeFormat(
                                user.createdAt
                              )}
                            </TableCell>
                            <TableCell align="right" data-label="Actions">
                              <Box className="commonTableActionBtns">
                                <Box
                                  className={
                                    !Boolean(rbAccess && rbAccess.UserEdit)
                                      ? " not-allowed"
                                      : ""
                                  }
                                >
                                  <Button
                                    onClick={() =>
                                      setOpenAddEdit({
                                        isShow: true,
                                        data: user,
                                        userId: user.id,
                                      })
                                    }
                                    variant="outlined"
                                    className={`emptyBtn ${
                                      !Boolean(rbAccess && rbAccess.UserEdit)
                                        ? "editIconBtnDisabled"
                                        : "editIconBtn"
                                    }`}
                                    startIcon={<DriveFileRenameOutlineIcon />}
                                    disabled={
                                      !Boolean(rbAccess && rbAccess.UserEdit)
                                    }
                                  ></Button>
                                </Box>
                                <Box
                                  className={
                                    !Boolean(rbAccess && rbAccess.UserDelete)
                                      ? " not-allowed"
                                      : ""
                                  }
                                >
                                  <Button
                                    variant="outlined"
                                    className={`emptyBtn ${
                                      (userInfo && userInfo.id === user.id) ||
                                      !Boolean(rbAccess && rbAccess.UserDelete)
                                        ? "deleteIconBtnDisabled"
                                        : "deleteIconBtn"
                                    }`}
                                    startIcon={<DeleteOutlineIcon />}
                                    onClick={() =>
                                      setShowConfirmPopup({
                                        isShow: true,
                                        data: user,
                                        userId: 0,
                                      })
                                    }
                                    disabled={
                                      (userInfo && userInfo.id === user.id) ||
                                      !Boolean(rbAccess && rbAccess.UserDelete)
                                    }
                                  ></Button>
                                </Box>
                              </Box>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <NoRowsFound />
                      )}
                    </TableBody>
                  </Table>
                  {/* <TablePagination
                    component="div"
                    count={100}
                    page={0}
                    onPageChange={handleChangePage}
                    rowsPerPage={rowsPerPage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                  /> */}
                  <Grid2
                    spacing={0}
                    direction="row"
                    alignItems="center"
                    justifyContent="center"
                    columnSpacing={{ xs: 2, md: 3 }}
                    container
                    height={50}
                  >
                    <Grid2 size={6} justifyItems={"flex-start"}>
                      {paginationResponseModel && (
                        <Typography className="pagination-Text">
                          {_applicationHelperService.getPaginationText(
                            paginationModel.pageNo,
                            paginationModel.offset,
                            paginationResponseModel.totalRecords
                          )}
                        </Typography>
                      )}
                    </Grid2>
                    <Grid2 size={6} justifyItems={"flex-end"}>
                      <Pagination
                        color="primary"
                        count={paginationResponseModel?.pageCount}
                        page={paginationModel.pageNo}
                        onChange={(
                          event: React.ChangeEvent<unknown>,
                          page: number
                        ) =>
                          setPaginationModel({
                            ...paginationModel,
                            pageNo: page,
                          })
                        }
                      />
                    </Grid2>
                  </Grid2>
                </TableContainer>
              </Box>
            </Box>
          </LeftMenuComponent>
        </Box>
      </Box>

      <GenericDrawer
        component={
          <AddEditUserComponent
            editData={openAddEdit.data}
            userId={openAddEdit.userId}
            callBack={(
              request: IUserRequestModel | undefined,
              resp: IUserCreationResponseModel | undefined
            ) => {
              fetchUsersPaginated();
              if (resp && resp.userId > 0) {
                setToastConfig(
                  ToastSeverity.Success,
                  MessageConstants.UserCreatedSuccessfully,
                  true
                );
                setTimeout(() => {
                  assignUser(request, resp.userId, false);
                }, 2000);
              } else {
                setOpenAddEdit({ isShow: false, data: null, userId: 0 });
              }
            }}
            showResetPasswordDialog={() => showResetPasswordDialog()}
          />
        }
        isShow={openAddEdit.isShow}
        callback={() =>
          setOpenAddEdit({ isShow: false, data: null, userId: 0 })
        }
      />
      {/* <Drawer
        anchor={"right"}
        open={openAddEdit.isShow}
        ModalProps={{
          onBackdropClick: () =>
            setOpenAddEdit({ isShow: false, data: null, userId: 0 }),
        }}
        sx={{
          "& .MuiDrawer-paper": {
            maxWidth: "400px", // Set the max width
            width: "100%", // Ensure the drawer does not exceed the max width
          },
          zIndex: (theme) => {
            return theme.zIndex.drawer + 1;
          },
        }}
        // PaperProps={{ style: { width: window.innerWidth * 0.25 } }}
      >
        {}
      </Drawer> */}

      {alertConfig && alertConfig.isShow && (
        <AlertDialog
          alertConfig={alertConfig}
          callBack={alertConfig.callBack}
        />
      )}

      {showConfirmPopup.data && (
        <ConfirmModel
          isOpen={showConfirmPopup.isShow}
          title="Delete User"
          description="Are you certain you want to delete this user? This action is irreversible."
          confirmText="Delete"
          cancelText="Cancel"
          cancelCallback={() =>
            setShowConfirmPopup({ isShow: false, data: null, userId: 0 })
          }
          confirmCallback={() => deleteRecord()}
        />
      )}
    </div>
  );
};

export default Users;
